package com.aug.usermanager.controller;

import com.aug.usermanager.dto.AdminLoginRequest;
import com.aug.usermanager.dto.ApiResponse;
import com.aug.usermanager.dto.UserStatistics;
import com.aug.usermanager.entity.User;
import com.aug.usermanager.security.JwtTokenProvider;
import com.aug.usermanager.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/admin")
public class AdminController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtTokenProvider jwtTokenProvider;
    
    /**
     * 管理员登录接口
     * POST /admin/login
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<Map<String, Object>>> adminLogin(@Valid @RequestBody AdminLoginRequest loginRequest) {
        try {
            // 简单的硬编码验证，实际项目中应该从数据库验证
            if (!"root".equals(loginRequest.getUsername()) || !"cll123".equals(loginRequest.getPassword())) {
                return ResponseEntity.badRequest().body(ApiResponse.validationError("用户名或密码错误"));
            }
            
            // 生成管理员token
            String token = jwtTokenProvider.generateToken("admin_root");
            
            Map<String, Object> result = new HashMap<>();
            result.put("username", "root");
            result.put("role", "admin");
            result.put("token", token);
            
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("登录失败：" + e.getMessage()));
        }
    }
    
    /**
     * 获取用户统计信息
     * GET /admin/statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<UserStatistics>> getUserStatistics(Authentication authentication) {
        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(ApiResponse.validationError("未授权访问"));
            }
            
            String userId = authentication.getName();
            if (!"admin_root".equals(userId)) {
                return ResponseEntity.status(403).body(ApiResponse.validationError("权限不足"));
            }
            
            UserStatistics statistics = userService.getUserStatistics();
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("获取统计信息失败：" + e.getMessage()));
        }
    }
    
    /**
     * 获取所有用户列表
     * GET /admin/users
     */
    @GetMapping("/users")
    public ResponseEntity<ApiResponse<List<User>>> getAllUsers(Authentication authentication) {
        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(ApiResponse.validationError("未授权访问"));
            }
            
            String userId = authentication.getName();
            if (!"admin_root".equals(userId)) {
                return ResponseEntity.status(403).body(ApiResponse.validationError("权限不足"));
            }
            
            List<User> users = userService.getAllUsers();
            return ResponseEntity.ok(ApiResponse.success(users));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("获取用户列表失败：" + e.getMessage()));
        }
    }
    
    /**
     * 删除用户
     * DELETE /admin/users/{userId}
     */
    @DeleteMapping("/users/{userId}")
    public ResponseEntity<ApiResponse<String>> deleteUser(@PathVariable String userId, Authentication authentication) {
        try {
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(401).body(ApiResponse.validationError("未授权访问"));
            }
            
            String adminUserId = authentication.getName();
            if (!"admin_root".equals(adminUserId)) {
                return ResponseEntity.status(403).body(ApiResponse.validationError("权限不足"));
            }
            
            userService.deleteUser(userId);
            return ResponseEntity.ok(ApiResponse.success("用户删除成功"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(ApiResponse.serverError("删除用户失败：" + e.getMessage()));
        }
    }
}
