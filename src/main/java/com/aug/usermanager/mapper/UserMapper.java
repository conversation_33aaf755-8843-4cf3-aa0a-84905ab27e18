package com.aug.usermanager.mapper;

import com.aug.usermanager.entity.User;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UserMapper {

    User findById(Long id);

    User findByUserId(String userId);

    User findByActivationCode(String activationCode);

    List<User> findAll();

    int insert(User user);

    int update(User user);

    int updateLastLoginTimeByUserId(String userId);

    int delete(Long id);
}

